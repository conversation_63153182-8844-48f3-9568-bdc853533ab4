import * as dotenv from 'dotenv';
import * as path from 'path';

const envPath = path.resolve(__dirname, '../.env');
console.log('Loading .env from:', envPath);

const result = dotenv.config({ path: envPath });

if (result.error) {
  console.error('Error loading .env:', result.error);
  process.exit(1);
}

console.log('Loaded environment variables:');
console.log('TELEGRAM_PHONE_NUMBER:', process.env.TELEGRAM_PHONE_NUMBER);
console.log('TELEGRAM_API_ID:', process.env.TELEGRAM_API_ID);
console.log('TELEGRAM_API_HASH:', process.env.TELEGRAM_API_HASH?.slice(0, 4) + '...' + process.env.TELEGRAM_API_HASH?.slice(-4)); 