{"name": "custom-group-creator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "next build"}, "dependencies": {"next": "14.1.0", "react": "18.2.0", "react-dom": "18.2.0", "@clerk/nextjs": "4.29.7", "@radix-ui/react-label": "2.0.2", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-slot": "1.0.2", "axios": "1.6.7", "class-variance-authority": "0.7.0", "clsx": "2.1.0", "lucide-react": "0.331.0", "sonner": "1.4.0", "tailwind-merge": "2.2.1", "tailwindcss-animate": "1.0.7"}, "devDependencies": {"@types/node": "20.11.19", "@types/react": "18.2.56", "@types/react-dom": "18.2.19", "autoprefixer": "10.4.17", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "postcss": "8.4.35", "tailwindcss": "3.4.1", "typescript": "5.3.3"}}