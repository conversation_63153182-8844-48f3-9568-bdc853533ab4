import { authMiddleware } from '@clerk/nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  groupCreationRateLimit,
  teamDataRateLimit,
  healthRateLimit,
  generalRateLimit
} from '@/lib/rate-limit';
import { generateCSPHeader } from '@/lib/sanitization';
import { auditLogger } from '@/lib/audit-logger';

export default authMiddleware({
  beforeAuth: async (req: NextRequest) => {
    // Apply rate limiting based on the path
    const { pathname } = req.nextUrl;

    try {
      // Rate limit specific API endpoints
      if (pathname.startsWith('/api/create-group')) {
        const rateLimitResponse = await groupCreationRateLimit(req);
        if (rateLimitResponse) {
          auditLogger.logSecurityEvent({
            userId: req.headers.get('x-user-id') || undefined,
            event: {
              type: 'rate_limit_exceeded',
              details: { endpoint: '/api/create-group', path: pathname },
              severity: 'medium'
            },
            req,
          });
          return rateLimitResponse;
        }
      } else if (pathname.startsWith('/api/team-data')) {
        const rateLimitResponse = await teamDataRateLimit(req);
        if (rateLimitResponse) {
          auditLogger.logSecurityEvent({
            userId: req.headers.get('x-user-id') || undefined,
            event: {
              type: 'rate_limit_exceeded',
              details: { endpoint: '/api/team-data', path: pathname },
              severity: 'medium'
            },
            req,
          });
          return rateLimitResponse;
        }
      } else if (pathname.startsWith('/api/health')) {
        const rateLimitResponse = await healthRateLimit(req);
        if (rateLimitResponse) {
          auditLogger.logSecurityEvent({
            userId: req.headers.get('x-user-id') || undefined,
            event: {
              type: 'rate_limit_exceeded',
              details: { endpoint: '/api/health', path: pathname },
              severity: 'medium'
            },
            req,
          });
          return rateLimitResponse;
        }
      } else if (pathname.startsWith('/api/')) {
        // General rate limit for other API endpoints
        const rateLimitResponse = await generalRateLimit(req);
        if (rateLimitResponse) {
          auditLogger.logSecurityEvent({
            userId: req.headers.get('x-user-id') || undefined,
            event: {
              type: 'rate_limit_exceeded',
              details: { endpoint: 'general_api', path: pathname },
              severity: 'medium'
            },
            req,
          });
          return rateLimitResponse;
        }
      }

      // Add security headers to all responses
      const response = NextResponse.next();

      // Content Security Policy - Updated to include Clerk development domains
      response.headers.set('Content-Security-Policy', generateCSPHeader());

      // Additional security headers
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
      response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

      return response;
    } catch (error) {
      console.error('Middleware error:', error);
      // Continue without middleware if there's an error
      const response = NextResponse.next();

      // Still add basic security headers even if rate limiting fails
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-Content-Type-Options', 'nosniff');

      return response;
    }
  },

  afterAuth: (auth, req) => {
    // Add user ID to headers for rate limiting
    if (auth.userId) {
      const response = NextResponse.next();
      response.headers.set('x-user-id', auth.userId);
      response.headers.set('x-user-email', auth.user?.emailAddresses?.[0]?.emailAddress || '');
      return response;
    }
    return NextResponse.next();
  }
});

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico|.*\\..*).*)'],
};