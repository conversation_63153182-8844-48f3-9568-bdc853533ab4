import { SignIn } from "@clerk/nextjs"
import { auth } from "@clerk/nextjs/server"
import dynamic from "next/dynamic"
import { Suspense } from "react"
import { FormSkeleton } from "@/components/ui/form-skeleton"

// Dynamic import for the main form component with loading state
const TelegramGroupFormRefactored = dynamic(
  () => import("@/components/forms/TelegramGroupFormRefactored").then(mod => ({ default: mod.TelegramGroupFormRefactored })),
  {
    loading: () => (
      <div className="min-h-screen bg-gray-50 py-8">
        <FormSkeleton />
      </div>
    ),
    ssr: false, // Disable SSR for this component to reduce initial bundle size
  }
)

export default async function Home() {
  const { userId } = await auth()

  if (userId) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <Suspense fallback={
          <div className="min-h-screen bg-gray-50 py-8">
            <FormSkeleton />
          </div>
        }>
          <TelegramGroupFormRefactored />
        </Suspense>
      </div>
    )
  }

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md px-4">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Welcome back</h2>
          <p className="mt-2 text-sm text-gray-600">
            Please sign in to continue to Telegram Group Creator
          </p>
        </div>
        <SignIn 
          appearance={{
            elements: {
              rootBox: "mx-auto",
              card: "bg-white shadow-xl border-0",
              formButtonPrimary: 
                "bg-blue-500 hover:bg-blue-600 text-sm normal-case",
              headerTitle: "hidden",
              headerSubtitle: "hidden",
              socialButtonsBlockButton: 
                "text-sm normal-case",
              formFieldInput: 
                "focus:border-blue-500 focus:ring-blue-500",
              dividerLine: "bg-gray-200",
              dividerText: "text-gray-500 text-sm",
            }
          }}
        />
      </div>
    </div>
  )
} 