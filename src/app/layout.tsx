import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignedIn,
  User<PERSON><PERSON><PERSON>
} from '@clerk/nextjs'
import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Toaster } from 'sonner'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Telegram Group Creator',
  description: 'Create Telegram groups easily',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={inter.className}>
          <SignedIn>
            <header className="border-b">
              <div className="container mx-auto px-4 py-3 flex items-center justify-between">
                <h1 className="text-xl font-semibold">Telegram Group Creator</h1>
                <UserButton 
                  afterSignOutUrl="/"
                  appearance={{
                    elements: {
                      avatarBox: "w-10 h-10"
                    }
                  }}
                />
              </div>
            </header>
          </SignedIn>
          {children}
          <Toaster />
        </body>
      </html>
    </ClerkProvider>
  )
}
