import { NextResponse } from "next/server";
import { Api } from "telegram";
import { initTelegramClient } from "@/lib/telegram";
import { createGroupRequestSchema } from "@/lib/validation";
import { formatUsername, getGroupName } from "@/types/team";
import { TelegramError, ValidationError, createErrorResponse, withErrorHandling, withRetry } from "@/lib/errors";
import { sanitizeFormData, sanitizeGroupMessage, logSanitization } from "@/lib/sanitization";
import { auditLogger } from "@/lib/audit-logger";
import type { CreateGroupRequest, CreateGroupResponse, FailedInvite } from "@/types/team";

/**
 * Create a new Telegram group with automated setup
 */

async function createGroupHandler(req: Request): Promise<NextResponse> {
  console.log('🚀 Starting group creation process...');

  // Parse and sanitize request body
  const rawRequest = await req.json();
  console.log('📝 Raw request received:', {
    projectName: rawRequest.projectName,
    salesRepUsername: rawRequest.salesRepUsername,
    outreachMemberUsernames: rawRequest.outreachMemberUsernames
  });

  // Extract user info for audit logging (from headers set by middleware)
  const userId = req.headers.get('x-user-id') || 'anonymous';
  const userEmail = req.headers.get('x-user-email') || undefined;

  // Sanitize input data
  const sanitizedRequest = sanitizeFormData(rawRequest);
  console.log('🧹 Request sanitized');

  // Validate request data
  let validatedRequest: CreateGroupRequest;
  try {
    validatedRequest = createGroupRequestSchema.parse(sanitizedRequest);
  } catch (validationError: any) {
    console.error('❌ Request validation failed:', validationError.errors);
    throw new ValidationError(
      `Invalid request data: ${validationError.errors?.[0]?.message || 'Validation failed'}`
    );
  }

  // Initialize Telegram client with retry
  const client = await withRetry(() => initTelegramClient(), 2, 1000);

    // Extract validated data
    const {
      projectName,
      projectLeads,
      salesRepUsername,
      salesRepEmoji = "",
      salesRepPAUsername,
      salesRepCalendlyLink,
      outreachMemberUsernames = [],
      outreachMemberEmoji = "",
      includeCalendly = false,
      welcomeMessage
    } = validatedRequest;

    // Format the group name
    const groupName = getGroupName(projectName, { emoji: salesRepEmoji } as any);
    console.log(`📝 Creating group: ${groupName}`);

  // Create the supergroup (a channel set as a megagroup)
  try {
    console.log(`📝 Creating supergroup: ${groupName}`);
    const result = await client.invoke(
      new Api.channels.CreateChannel({
        title: groupName,
        about: "IBC Group Discussion",
        megagroup: true,
      })
    );

    // Get the created channel
    const updates = result as any;
    if (!updates.chats || updates.chats.length === 0) {
      throw new TelegramError("Failed to create the group - no chat returned");
    }
    const channel = updates.chats[0];
    console.log(`✅ Group created with ID: ${channel.id}`);

    // Format and validate usernames
    const salesRep = formatUsername(salesRepUsername);
    const salesRepPA = formatUsername(salesRepPAUsername || "");
    const outreachMembers = outreachMemberUsernames
      .map((username: string) => formatUsername(username))
      .filter(Boolean) as string[];

    // Format project leads
    const formattedProjectLeads = projectLeads
      .map((username: string) => formatUsername(username))
      .filter(Boolean) as string[];

    const firstProjectLead = formattedProjectLeads[0] || "";

    console.log('👥 Formatted usernames:', {
      salesRep,
      salesRepPA,
      outreachMembers,
      projectLeads: formattedProjectLeads
    });

    // Define the member lists - outreach member and PA are admins
    const adminMembers = ["@IbcAdmin_Bot", salesRep, salesRepPA, ...outreachMembers].filter(Boolean) as string[];
    const regularMembers = formattedProjectLeads; // Add all project leads as regular members

    console.log("👥 Adding members to group...");
    // Track failed invites to send /invite_new_group command for them later
    const failedInvites: FailedInvite[] = [];

    // Add admin members first
    for (const member of adminMembers) {
      try {
        console.log(`👤 Attempting to add admin member: ${member}`);
        const user = await client.getEntity(member);

        await withRetry(async () => {
          await client.invoke(
            new Api.channels.InviteToChannel({
              channel: channel,
              users: [user],
            })
          );
        }, 2, 500);

        console.log(`✅ Successfully added member: ${member}`);

        // Make admin members admins
        try {
          await client.invoke(
            new Api.channels.EditAdmin({
              channel: channel,
              userId: user,
              adminRights: new Api.ChatAdminRights({
                changeInfo: true,
                postMessages: true,
                editMessages: true,
                deleteMessages: true,
                banUsers: true,
                inviteUsers: true,
                pinMessages: true,
                addAdmins: false,
                anonymous: false,
                manageCall: true,
                other: true,
              }),
              rank: member === "@IbcAdmin_Bot" ? "Bot Admin" :
                    outreachMembers.includes(member) ? "Outreach Admin" :
                    member === salesRepPA ? "PA Admin" : "Admin"
            })
          );
          console.log(`👑 Successfully made ${member} an admin`);
        } catch (error: any) {
          console.error(`⚠️ Error making ${member} an admin:`, error.message);
        }
      } catch (error: any) {
        console.error(`❌ Error adding member ${member}:`, error.message);
        const telegramError = TelegramError.fromTelegramApiError(error);
        failedInvites.push({ username: member, reason: telegramError.message });
        continue;
      }
    }

    // Add regular members
    for (const member of regularMembers) {
      try {
        console.log(`👤 Attempting to add regular member: ${member}`);
        const user = await client.getEntity(member);

        await withRetry(async () => {
          await client.invoke(
            new Api.channels.InviteToChannel({
              channel: channel,
              users: [user],
            })
          );
        }, 2, 500);

        console.log(`✅ Successfully added regular member: ${member}`);
      } catch (error: any) {
        console.error(`❌ Error adding regular member ${member}:`, error.message);
        const telegramError = TelegramError.fromTelegramApiError(error);
        failedInvites.push({ username: member, reason: telegramError.message });
        continue;
      }
    }

    // Send welcome message
    try {
      console.log("📨 Sending welcome message...");

      // Sanitize welcome message before sending
      const sanitizedWelcomeMessage = sanitizeGroupMessage(welcomeMessage);
      logSanitization(welcomeMessage, sanitizedWelcomeMessage, 'welcomeMessage', userId);

      // Send welcome message
      await client.sendMessage(channel, {
        message: sanitizedWelcomeMessage
      });
      console.log("✅ Welcome message sent successfully");

      // Send IBC Ventures info message
      const ibcVenturesMessage = `IBC VENTURES:

Led by Mario Nawfal, founder of IBC (established in 2017, latest valuation at $400m), as well as industry veterans, and over 200 employees, we are proud to host the largest shows on X and actively invest, incubate, and accelerate Web3 and AI projects, boasting a venture portfolio of over 300 companies, including multiple unicorns.

Our incubator has leaped to over 20 incubatees in less than 5 months, with already 3 INCUBATED unicorns under our belt.

🏛 X Shows
With 2-4 billion monthly impressions and millions of weekly listeners across all formats, we consistently rank in the global top 10 accounts on ALL social media platforms (several times reaching #4). This includes TikTok, Youtube, Instagram and X.

Our shows have featured iconic guests, including:
Elon, RFK, Hunter Biden, Mark Cuban, CZ, Prince Alwaleed, Marc Andreessen, Bill Ackman, Vivek, David Sacks, and many world leaders and politicians.

🏛 Our Growth
🟢 Marketed, launched, and accelerated over 300 projects to date, with nearly 200 in 2024 alone.
🟢 Expanded our incubator to include over 20 startups in less than six months, including three unicorns.
🟢 Collaborated with over 50 memecoin projects, with our latest being the top-performing memecoin at the end of 2024.

🏛 AI Innovation
At IBC, our obsession with AI is driving new ventures. Having worked with the best in the industry, we are:
✅ Launching our own AI initiatives, in partnerships with the biggest project in web3 crypto.
✅ Introducing a new AI video show next month featuring the biggest names in the industry. This is on top of our existing DAILY web3 AI show.
✅Actively seeking innovative AI projects to incubate, invest in, and expose to the masses.

🏛 Media Assets
As one of the largest media companies on X, we leverage multiple assets, including:
🚀Mario Nawfal
🚀The Crypto Town Hall (A daily show with Mario Nawfal, Scott Melker , and Ran aka CryptoBanter)
🚀The Roundtable Show
and are actively acquiring more media assets.

🏛 Web3 Consulting, Advisory, and Marketing
Since 2017, we've been at the forefront of Web3 marketing, delivering proven results for Binance-launched projects, established protocols, and leading memecoins. Our Go-To-Market expertise is unmatched.This includes Go To Market, KOL marketing, fundraising, market making, listing, and community building. We're confident enough to now say we are the best in the space, or at least one of the best, at what we do.

🏛 IBC Group Accelerator
The ONLY media-led accelerator, launchpad, and spot exchange in the world. We provide startups with a clear path from ideation to token launch, leveraging one of the most experienced teams in Web3.

🏛 Memecoin/AI agent Incubation

We've worked with some of the biggest memecoins in the space, including 2 top 10 memes

Some of the memes that we can share are Maneki, Ben the Dog, Doger, Brett on Base, Maga ($700m), Shiba Saga, Biao, Luigi Mangioni, Occupy Mars, Dragon, and many more to list

And many other AI agents including Gekko, DegentAI, Holozone, Chai, and a few large ones we cannot share

We not only have the most reach, but work with some of the largest alpha groups to generate buying power for projects, and have a close relationship with each and every major exchange.

For more information, message us or visit our website:
👉🏻 https://www.ibcgroup.io/`;

      await client.sendMessage(channel, {
        message: ibcVenturesMessage
      });
      
      // Send follow-up Calendly scheduling message
      if (includeCalendly && salesRepCalendlyLink) {
        const calendlyMessage = `We'd be glad to connect for a brief introductory call to discuss things further and explore potential opportunities for collaboration at your convenience.

Please let me know if this timing works for you to schedule a call with our Partnership Executive at IBC:
${salesRepCalendlyLink}`;

        await client.sendMessage(channel, {
          message: calendlyMessage
        });
        console.log("📅 Calendly message sent successfully");
      }
      console.log("✅ IBC Ventures info message sent successfully");
    } catch (error: any) {
      console.error("❌ Error sending messages:", error.message);
    }

    // Execute /optin command
    try {
      await client.sendMessage(channel, {
        message: "/optin"
      });
      console.log("🤖 Optin command sent");
    } catch (error: any) {
      console.error("❌ Error sending optin command:", error.message);
    }

    // If PA exists, get their user ID and execute /setadmin command
    if (salesRepPA) {
      try {
        console.log(`👑 Setting up PA admin: ${salesRepPA}`);
        const paUser = await client.getEntity(salesRepPA);
        if (paUser) {
          await client.sendMessage(channel, {
            message: `/setadmin ${paUser.id}`
          });
          console.log(`✅ Setadmin command sent for PA: ${salesRepPA}`);
        }
      } catch (error: any) {
        console.error(`❌ Error executing setadmin command for PA: ${error.message}`);
        const telegramError = TelegramError.fromTelegramApiError(error);
        failedInvites.push({ username: salesRepPA, reason: telegramError.message });
      }
    }

    // If sales rep exists, execute /invite_new_group command with their user ID
    if (salesRep) {
      try {
        console.log(`📨 Inviting sales rep: ${salesRep}`);
        const salesRepUser = await client.getEntity(salesRep);
        if (salesRepUser) {
          await client.sendMessage(channel, {
            message: `/invite_new_group ${salesRepUser.id}`
          });
          console.log(`✅ Invite_new_group command sent for Sales Rep: ${salesRep}`);
        }
      } catch (error: any) {
        console.error(`❌ Error executing invite_new_group command for Sales Rep: ${error.message}`);
        const telegramError = TelegramError.fromTelegramApiError(error);
        failedInvites.push({ username: salesRep, reason: telegramError.message });
      }
    }


  console.log("🎉 Group creation completed successfully!");

  // Log successful group creation
  auditLogger.logGroupCreation({
    userId,
    userEmail,
    projectName: validatedRequest.projectName,
    groupId: String(channel.id),
    salesRep: validatedRequest.salesRepUsername,
    outreachMembers: validatedRequest.outreachMemberUsernames || [],
    success: true,
    req,
  });

  return NextResponse.json<CreateGroupResponse>({
    success: true,
    group_id: String(channel.id),
    group_name: groupName,
    message: "Group created successfully",
    added_members: {
      admins: adminMembers,
      regular: regularMembers,
    },
    failed_invites: failedInvites
  });

  } catch (error: any) {
    throw TelegramError.fromTelegramApiError(error);
  }
}
export const POST = withErrorHandling(async (req: Request) => {
  try {
    return await createGroupHandler(req);
  } catch (error: any) {
    console.error("❌ Error in group creation:", error);

    // Extract user info and request data for audit logging
    const userId = req.headers.get('x-user-id') || undefined;
    const userEmail = req.headers.get('x-user-email') || undefined;

    try {
      const rawRequest = await req.json();
      // Log failed group creation
      auditLogger.logGroupCreation({
        userId,
        userEmail,
        projectName: rawRequest.projectName || 'unknown',
        salesRep: rawRequest.salesRepUsername || 'unknown',
        outreachMembers: rawRequest.outreachMemberUsernames || [],
        success: false,
        errorMessage: error.message,
        req,
      });
    } catch (parseError) {
      // If we can't parse the request, log a general failure
      auditLogger.log({
        userId,
        userEmail,
        action: 'create_telegram_group',
        resource: 'telegram_group',
        details: { parseError: 'Failed to parse request body' },
        success: false,
        errorMessage: error.message,
        severity: 'medium',
        category: 'group_creation',
      });
    }

    const errorResponse = createErrorResponse(error, {
      operation: 'create_telegram_group',
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(errorResponse, {
      status: error.statusCode || 500
    });
  }
});