import { NextResponse } from "next/server";
import { initTelegramClient } from "@/lib/telegram";

export async function GET() {
  try {
    const client = await initTelegramClient();
    const me = await client.getMe();
    
    return NextResponse.json({
      status: "healthy",
      user_info: JSON.stringify(me),
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
} 