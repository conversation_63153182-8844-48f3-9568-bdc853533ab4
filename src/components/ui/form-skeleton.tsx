import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

export function FormSkeleton() {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4 mx-auto"></div>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Project Details Section */}
        <div>
          <div className="h-6 bg-gray-200 rounded animate-pulse mb-4 w-1/4"></div>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Outreach Team Section */}
        <div>
          <div className="h-6 bg-gray-200 rounded animate-pulse mb-4 w-1/4"></div>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Partnership Representative Section */}
        <div>
          <div className="h-6 bg-gray-200 rounded animate-pulse mb-4 w-1/3"></div>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Preview Section */}
        <div>
          <div className="h-6 bg-gray-200 rounded animate-pulse mb-4 w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
        </div>

        {/* Submit Button */}
        <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
      </CardContent>
    </Card>
  )
}

export function SectionSkeleton({ title }: { title?: string }) {
  return (
    <div className="space-y-4">
      {title && (
        <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4"></div>
      )}
      <div className="space-y-3">
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded animate-pulse w-3/4"></div>
      </div>
    </div>
  )
}
