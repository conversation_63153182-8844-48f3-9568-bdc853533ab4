"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useUser } from "@clerk/nextjs"
import { useState, useEffect } from "react"
import axios from "axios"
import { toast } from "sonner"
import type {
  SalesRep,
  OutreachMember,
  FormData,
  TeamDataResponse,
  CreateGroupRequest,
  CreateGroupResponse,
  GroupCreationResult
} from "@/types/team"
import { validateUsername, validateProjectName, validateCalendlyLink } from "@/lib/validation"
import { sanitizeProjectName, sanitizeUsername, sanitizeUrl } from "@/lib/sanitization"
import { getGroupName, findSalesRepByUsername, findOutreachMemberByName } from "@/types/team"



export function TelegramGroupForm() {
  const { user } = useUser()

  const [salesReps, setSalesReps] = useState<SalesRep[]>([]);
  const [outreachMembers, setOutreachMembers] = useState<OutreachMember[]>([]);
  const [isLoadingTeamData, setIsLoadingTeamData] = useState(true);

  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        setIsLoadingTeamData(true);
        const response = await axios.get<TeamDataResponse>('/api/team-data');
        console.log('📊 Team data response:', response.data);

        // Filter out sales reps without usernames
        const filteredSalesReps = (response.data.salesReps || []).filter(
          (rep: SalesRep) => rep.username && rep.username.trim() !== ""
        );
        console.log('✅ Filtered salesReps:', filteredSalesReps);

        setSalesReps(filteredSalesReps);
        setOutreachMembers(response.data.outreachMembers || []);

        if (response.data.warning) {
          toast.warning(response.data.warning);
        }
      } catch (error) {
        console.error('❌ Failed to fetch team data:', error);
        toast.error('Failed to load team data. Some features may not work properly.');
      } finally {
        setIsLoadingTeamData(false);
      }
    };
    fetchTeamData();
  }, []);
  const [isLoading, setIsLoading] = useState(false);
  const [creationResult, setCreationResult] = useState<GroupCreationResult | null>(null);

  const [formData, setFormData] = useState<FormData>({
    projectName: "",
    projectLeads: [""],
    outreachTeamMember: "",
    outreachMemberName: "",
    outreachMemberUsername: "",
    customOutreachUsername: "",
    useCustomOutreachUsername: false,
    salesperson: "",
    customSalesRepUsername: "",
    useCustomSalesRepUsername: false,
    enterSalesRepManually: false,
    customCalendlyLink: "",
    includeCalendly: true,
    errors: {
      projectName: "",
      projectLead: "",
      outreachTeamMember: "",
      salesperson: "",
      outreachMemberUsername: "",
      customOutreachUsername: "",
      customSalesRepUsername: "",
      customCalendlyLink: ""
    }
  })

  useEffect(() => {
    if (user?.fullName) {
      setFormData(prev => ({
        ...prev,
        outreachTeamMember: user.fullName || ""
      }))
    }
  }, [user])

  const selectedSalesRep = findSalesRepByUsername(salesReps, formData.salesperson)
  const selectedOutreachMember = findOutreachMemberByName(outreachMembers, formData.outreachMemberName)

  const validateForm = () => {
    const errors = {
      projectName: "",
      projectLead: "",
      outreachTeamMember: "",
      salesperson: "",
      outreachMemberUsername: "",
      customOutreachUsername: "",
      customSalesRepUsername: "",
      customCalendlyLink: ""
    }

    // Validate project name
    const projectNameValidation = validateProjectName(formData.projectName);
    if (!projectNameValidation.isValid) {
      errors.projectName = projectNameValidation.error || "Invalid project name";
    }

    // Validate project leads
    const projectLeadsArray = formData.projectLeads
      .map(s => s.trim())
      .filter(s => s.length > 0);

    if (projectLeadsArray.length === 0) {
      errors.projectLead = "At least one project lead username is required";
    } else {
      for (const username of projectLeadsArray) {
        const validation = validateUsername(username);
        if (!validation.isValid) {
          errors.projectLead = validation.error || "Invalid username format";
          break;
        }
      }
    }

    // Validate outreach member
    if (formData.useCustomOutreachUsername) {
      if (!formData.customOutreachUsername) {
        errors.customOutreachUsername = "Custom BDR username is required";
      } else {
        const validation = validateUsername(formData.customOutreachUsername);
        if (!validation.isValid) {
          errors.customOutreachUsername = validation.error || "Invalid username format";
        }
      }
    } else {
      if (!formData.outreachMemberName || !formData.outreachMemberUsername) {
        errors.outreachMemberUsername = "Outreach member and username are required";
      }
    }

    // Validate sales representative
    if (formData.enterSalesRepManually) {
      if (!formData.customSalesRepUsername) {
        errors.customSalesRepUsername = "Sales rep username is required";
      } else {
        const validation = validateUsername(formData.customSalesRepUsername);
        if (!validation.isValid) {
          errors.customSalesRepUsername = validation.error || "Invalid username format";
        }
      }
    } else if (!formData.salesperson) {
      errors.salesperson = "Sales representative is required";
    } else if (formData.useCustomSalesRepUsername) {
      if (!formData.customSalesRepUsername) {
        errors.customSalesRepUsername = "Custom sales rep username is required";
      } else {
        const validation = validateUsername(formData.customSalesRepUsername);
        if (!validation.isValid) {
          errors.customSalesRepUsername = validation.error || "Invalid username format";
        }
      }
    }

    // Validate Calendly link if provided
    if (formData.customCalendlyLink) {
      const validation = validateCalendlyLink(formData.customCalendlyLink);
      if (!validation.isValid) {
        errors.customCalendlyLink = validation.error || "Invalid Calendly link";
      }
    }

    setFormData(prev => ({ ...prev, errors }))
    return !Object.values(errors).some(error => error)
  }

  // getGroupName function is now imported from types/team.ts

  // getCalendlyLink removed as per requirements

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true);
    setCreationResult(null);

    try {
      console.log('🚀 Starting form submission...');

      // Get the appropriate calendly link
      let calendlyLink: string | undefined;
      if (formData.enterSalesRepManually) {
        calendlyLink = formData.customCalendlyLink || undefined;
      } else if (selectedSalesRep) {
        calendlyLink = selectedSalesRep.calendarLink;
      }

      // Get the appropriate outreach member username
      const outreachUsername = formData.useCustomOutreachUsername
        ? formData.customOutreachUsername
        : formData.outreachMemberUsername;

      // Get the appropriate sales rep username
      const salesRepUsername = formData.enterSalesRepManually
        ? formData.customSalesRepUsername
        : (formData.useCustomSalesRepUsername && selectedSalesRep
          ? formData.customSalesRepUsername
          : selectedSalesRep?.username);

      if (!salesRepUsername) {
        toast.error('Sales representative username is required');
        return;
      }
      
      // Create welcome message with the exact format provided
      const projectLeadsArray = formData.projectLeads
        .map(s => s.trim())
        .filter(s => s.length > 0);

      const welcomeMessage = `👥 Project Lead: ${projectLeadsArray.join(", ")}
👨‍💼 Partnership Representative: ${salesRepUsername}
👨‍💻 BDR: ${outreachUsername}${formData.includeCalendly && calendlyLink ? `
📅 Schedule a meeting: ${calendlyLink}` : ''}`;

      const requestBody: CreateGroupRequest = {
        projectName: formData.projectName,
        projectLeads: projectLeadsArray,
        outreachTeamMember: outreachUsername,
        outreachMemberEmail: selectedOutreachMember?.email,
        salesperson: formData.enterSalesRepManually ? "Custom Sales Rep" : formData.salesperson,
        salesRepUsername: salesRepUsername,
        paUsername: formData.enterSalesRepManually ? undefined : selectedSalesRep?.paUsername,
        calendlyLink: formData.includeCalendly ? calendlyLink : undefined,
        includeCalendly: formData.includeCalendly,
        welcomeMessage: welcomeMessage,
        inviteSalesRep: true,

        // Additional fields for Sales Representative
        salesRepEmoji: formData.enterSalesRepManually ? undefined : selectedSalesRep?.emoji,
        salesRepPAUsername: formData.enterSalesRepManually ? undefined : selectedSalesRep?.paUsername,
        salesRepCalendlyLink: formData.enterSalesRepManually
          ? formData.customCalendlyLink
          : (formData.includeCalendly && selectedSalesRep?.calendarLink ? selectedSalesRep.calendarLink : undefined),
        salesRepCategory: formData.enterSalesRepManually ? undefined : selectedSalesRep?.tier,

        // Additional fields for Outreach Member
        outreachMemberUsernames: selectedOutreachMember?.telegramUsernames,
        outreachMemberEmoji: selectedOutreachMember?.emoji
      };
      console.log('📤 Sending request:', requestBody);
      const response = await axios.post<CreateGroupResponse>("/api/create-group", requestBody);

      if (response.data.success) {
        // Show success message
        toast.success("🎉 Telegram group created successfully!");

        // Show detailed failure reasons if any users couldn't be added
        if (response.data.failed_invites && response.data.failed_invites.length > 0) {
          console.log('⚠️ Some users could not be added:', response.data.failed_invites);

          // Show individual failure reasons
          response.data.failed_invites.forEach((failedInvite, index) => {
            setTimeout(() => {
              toast.error(
                `❌ Could not add ${failedInvite.username}: ${failedInvite.reason}`,
                {
                  duration: 8000, // Show for 8 seconds
                  id: `failed-invite-${index}`, // Unique ID to prevent duplicates
                }
              );
            }, index * 500); // Stagger the toasts by 500ms each
          });

          // Show summary toast
          setTimeout(() => {
            toast.warning(
              `⚠️ ${response.data.failed_invites?.length || 0} user(s) could not be added automatically. Check the error messages above for details.`,
              {
                duration: 10000, // Show for 10 seconds
                id: 'failed-invites-summary',
              }
            );
          }, (response.data.failed_invites?.length || 0) * 500 + 1000);
        }

        setCreationResult({
          success: true,
          failed_invites: response.data.failed_invites
        });

        // Reset form
        setFormData({
          projectName: "",
          projectLeads: [""],
          outreachTeamMember: "",
          outreachMemberName: "",
          outreachMemberUsername: "",
          customOutreachUsername: "",
          useCustomOutreachUsername: false,
          salesperson: "",
          customSalesRepUsername: "",
          useCustomSalesRepUsername: false,
          enterSalesRepManually: false,
          customCalendlyLink: "",
          includeCalendly: true,
          errors: {
            projectName: "",
            projectLead: "",
            outreachTeamMember: "",
            salesperson: "",
            outreachMemberUsername: "",
            customOutreachUsername: "",
            customSalesRepUsername: "",
            customCalendlyLink: ""
          }
        });
      } else {
        toast.error(response.data.error || "Failed to create group");
      }
    } catch (error: any) {
      console.error("❌ Error creating group:", error);

      const errorMessage = error.response?.data?.error ||
        error.message ||
        "Failed to create Telegram group. Please try again.";

      toast.error(errorMessage);

      setCreationResult({
        success: false,
        failed_invites: []
      });
    } finally {
      setIsLoading(false);
    }
  }
console.log('Sales Representatives:', salesReps);
return (
    <div className="container mx-auto p-4 max-w-3xl">
      {/* Loading spinner with simplified conditional rendering */}
      <div>
        {isLoading ? (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-white/90 rounded-xl p-8 max-w-md w-full mx-4 text-center shadow-2xl">
              <div className="flex flex-col items-center justify-center gap-6">
                <div className="relative w-24 h-24">
                  <div className="absolute inset-0 rounded-full border-8 border-gray-200"></div>
                  <div className="absolute inset-0 rounded-full border-8 border-blue-600 animate-spin border-t-transparent"></div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-blue-600">Creating Your Telegram Group</h3>
                  <p className="text-gray-600 mt-3">
                    Please wait while we set up your group with all required members and settings...
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Create Telegram Group
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Notification section with simplified conditional rendering */}
          <div>
            {creationResult && creationResult.success && creationResult.failed_invites && creationResult.failed_invites.length > 0 ? (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 className="text-amber-700 font-medium">⚠️ Some members couldn't be invited automatically</h3>
                <p className="text-sm text-amber-600 mt-1">
                  The following users could not be added to the group. The bot has automatically sent /invite_new_group commands for them:
                </p>
                <div className="mt-3 space-y-2">
                  {creationResult.failed_invites.map((invite, index) => (
                    <div key={index} className="bg-white p-3 rounded border border-amber-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <span className="font-medium text-amber-800">{invite.username}</span>
                          <p className="text-xs text-amber-600 mt-1">
                            <span className="font-medium">Reason:</span> {invite.reason}
                          </p>
                        </div>
                        <span className="text-amber-500 text-xs bg-amber-100 px-2 py-1 rounded">
                          Auto-invited
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-amber-600 mt-3 italic">
                  💡 These users will receive automatic invitations through the bot commands sent in the group chat.
                </p>
              </div>
            ) : null}
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Project Name */}
            <div className="space-y-2">
              <Label htmlFor="projectName">Project Name</Label>
              <Input
                id="projectName"
                value={formData.projectName}
                onChange={(e) => {
                  // Allow typing freely, only basic validation
                  const rawValue = e.target.value;
                  setFormData({
                    ...formData,
                    projectName: rawValue,
                    errors: {
                      ...formData.errors,
                      projectName: rawValue.length === 0 ? "Project name is required" : ""
                    }
                  });
                }}
                onBlur={(e) => {
                  // Sanitize and validate when user finishes typing
                  const rawValue = e.target.value;
                  const sanitizedValue = sanitizeProjectName(rawValue);
                  const validation = validateProjectName(sanitizedValue);
                  setFormData({
                    ...formData,
                    projectName: sanitizedValue,
                    errors: {
                      ...formData.errors,
                      projectName: validation.isValid ? "" : (validation.error || "Invalid project name")
                    }
                  });
                }}
                placeholder="Enter project name (e.g., Test Team)"
                className={formData.errors.projectName ? "border-red-500" : ""}
              />
              {formData.errors.projectName && (
                <p className="text-sm text-red-500">{formData.errors.projectName}</p>
              )}
              {formData.projectName && !formData.errors.projectName && (
                <p className="text-sm text-gray-500">
                  Group will be created as: {getGroupName(formData.projectName, selectedSalesRep)}
                </p>
              )}
            </div>

            {/* Project Lead */}
            <div className="space-y-2">
              <Label htmlFor="projectLead">Project Lead Telegram Usernames</Label>
              {formData.projectLeads.map((lead, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <Input
                    value={lead}
                    onChange={(e) => {
                      const rawValue = e.target.value;
                      const sanitizedValue = sanitizeUsername(rawValue);
                      const newLeads = [...formData.projectLeads];
                      newLeads[index] = sanitizedValue;

                      // Validate all project leads
                      const validLeads = newLeads.filter(l => l.trim().length > 0);
                      let error = "";
                      if (validLeads.length === 0) {
                        error = "At least one project lead is required";
                      } else {
                        for (const username of validLeads) {
                          const validation = validateUsername(username);
                          if (!validation.isValid) {
                            error = validation.error || "Invalid username format";
                            break;
                          }
                        }
                      }

                      setFormData({
                        ...formData,
                        projectLeads: newLeads,
                        errors: { ...formData.errors, projectLead: error }
                      });
                    }}
                    placeholder="@username"
                    className={`flex-1 ${formData.errors.projectLead ? "border-red-500" : ""}`}
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      if (formData.projectLeads.length === 1) return;
                      const newLeads = formData.projectLeads.filter((_, i) => i !== index);
                      setFormData({
                        ...formData,
                        projectLeads: newLeads,
                        errors: { ...formData.errors, projectLead: "" }
                      });
                    }}
                    disabled={formData.projectLeads.length === 1}
                  >
                    –
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  setFormData({
                    ...formData,
                    projectLeads: [...formData.projectLeads, ""],
                    errors: { ...formData.errors, projectLead: "" }
                  });
                }}
              >
                + Add Project Lead
              </Button>
              {formData.errors.projectLead && (
                <p className="text-sm text-red-500">{formData.errors.projectLead}</p>
              )}
            </div>

            {/* Outreach Member Selection */}
            <div className="space-y-2">
              <Label htmlFor="outreachMember">Outreach Team Member</Label>
              <div className="flex items-center space-x-2 mb-2">
                <input
                  type="checkbox"
                  id="useCustomOutreachUsername"
                  checked={formData.useCustomOutreachUsername}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      useCustomOutreachUsername: e.target.checked,
                      errors: { ...formData.errors, outreachMemberUsername: "", customOutreachUsername: "" }
                    })
                  }
                  className="h-4 w-4"
                />
                <Label htmlFor="useCustomOutreachUsername">Enter BDR username manually</Label>
              </div>
              
              {!formData.useCustomOutreachUsername ? (
                <>
                  <Select
                    value={formData.outreachMemberName}
                    onValueChange={(value) =>
                      setFormData({ 
                        ...formData, 
                        outreachMemberName: value,
                        outreachMemberUsername: "",
                        errors: { ...formData.errors, outreachMemberUsername: "" }
                      })
                    }
                  >
                    <SelectTrigger 
                      id="outreachMember"
                      className={formData.errors.outreachMemberUsername ? "border-red-500" : ""}
                    >
                      <SelectValue placeholder="Select an outreach member" />
                    </SelectTrigger>
                    <SelectContent>
                      {outreachMembers.map((member: OutreachMember, idx: number) => (
                        <SelectItem key={idx} value={member.name}>
                          {member.emoji} {member.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {selectedOutreachMember && (
                    <div className="mt-2">
                      <Label htmlFor="outreachUsername">Telegram Username</Label>
                      <div className="mt-2 p-4 bg-gray-50 rounded-lg space-y-2">
                        <p className="text-sm font-medium">
                          {selectedOutreachMember.emoji} {selectedOutreachMember.name}
                        </p>
                        <Select
                          value={formData.outreachMemberUsername}
                          onValueChange={(value) =>
                            setFormData({ 
                              ...formData, 
                              outreachMemberUsername: value,
                              errors: { ...formData.errors, outreachMemberUsername: "" }
                            })
                          }
                        >
                          <SelectTrigger id="outreachUsername">
                            <SelectValue placeholder="Select username" />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedOutreachMember?.telegramUsernames
                              ?.filter((username: string) => username)
                              .map((username: string) => (
                                <SelectItem key={username} value={username}>
                                  {username}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="customOutreachUsername">Custom BDR Telegram Username</Label>
                  <Input
                    id="customOutreachUsername"
                    value={formData.customOutreachUsername}
                    onChange={(e) => {
                      const value = e.target.value;
                      const validation = validateUsername(value);
                      setFormData({
                        ...formData,
                        customOutreachUsername: value,
                        errors: {
                          ...formData.errors,
                          customOutreachUsername: validation.isValid ? "" : (validation.error || "Invalid username")
                        }
                      });
                    }}
                    placeholder="@username"
                    className={formData.errors.customOutreachUsername ? "border-red-500" : ""}
                  />
                  {formData.errors.customOutreachUsername && (
                    <p className="text-sm text-red-500">{formData.errors.customOutreachUsername}</p>
                  )}
                </div>
              )}

              {formData.errors.outreachMemberUsername && (
                <p className="text-sm text-red-500">{formData.errors.outreachMemberUsername}</p>
              )}
            </div>

            {/* Salesperson Dropdown */}
            <div className="space-y-2">
              <Label htmlFor="salesperson">Sales Representative</Label>
              <div className="flex items-center space-x-2 mb-2">
                <input
                  type="checkbox"
                  id="enterSalesRepManually"
                  checked={formData.enterSalesRepManually}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      enterSalesRepManually: e.target.checked,
                      errors: { ...formData.errors, customSalesRepUsername: "" }
                    })
                  }
                  className="h-4 w-4"
                />
                <Label htmlFor="enterSalesRepManually">Enter Partnership Representative manually</Label>
              </div>
              {!formData.enterSalesRepManually ? (
                isLoadingTeamData ? (
                  <div className="flex items-center space-x-2 p-3 border rounded-md">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm text-gray-500">Loading sales representatives...</span>
                  </div>
                ) : salesReps && salesReps.length > 0 ? (
                  <Select
                    value={formData.salesperson}
                    onValueChange={(value) =>
                      setFormData({
                        ...formData,
                        salesperson: value,
                        errors: { ...formData.errors, salesperson: "" }
                      })
                    }
                  >
                    <SelectTrigger
                      id="salesperson"
                      className={formData.errors.salesperson ? "border-red-500" : ""}
                    >
                      <SelectValue placeholder="Select a sales representative" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* Populate Sales Representative dropdown from salesReps fetched from /api/team-data */}
                      {salesReps
                        .filter((rep: SalesRep) => rep.username)
                        .map((rep: SalesRep, idx: number) => (
                          <SelectItem key={idx} value={rep.username}>
                            {`${rep.emoji} ${rep.name}`}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-3 border rounded-md bg-yellow-50 border-yellow-200">
                    <p className="text-sm text-yellow-800">
                      ⚠️ No sales representatives available. Please check your Google Sheets configuration.
                    </p>
                  </div>
                )
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="customSalesRepUsername">Partnership Representative Username</Label>
                  <Input
                    id="customSalesRepUsername"
                    value={formData.customSalesRepUsername}
                    onChange={(e) =>
                      setFormData({ 
                        ...formData, 
                        customSalesRepUsername: e.target.value,
                        errors: { ...formData.errors, customSalesRepUsername: "" }
                      })
                    }
                    placeholder="@username"
                    className={formData.errors.customSalesRepUsername ? "border-red-500" : ""}
                  />
                  {formData.errors.customSalesRepUsername && (
                    <p className="text-sm text-red-500">{formData.errors.customSalesRepUsername}</p>
                  )}

                  <div className="flex items-center space-x-4 mt-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="includeCalendlyManual"
                        checked={formData.includeCalendly}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            includeCalendly: e.target.checked
                          })
                        }
                        className="h-4 w-4"
                      />
                      <Label htmlFor="includeCalendlyManual">Include Calendly Link</Label>
                    </div>
                  </div>
                  
                  {formData.includeCalendly && (
                    <div className="mt-2">
                      <Label htmlFor="customCalendlyLink">Calendly Link</Label>
                      <Input
                        id="customCalendlyLink"
                        value={formData.customCalendlyLink}
                        onChange={(e) =>
                          setFormData({ 
                            ...formData, 
                            customCalendlyLink: e.target.value,
                            errors: { ...formData.errors, customCalendlyLink: "" }
                          })
                        }
                        placeholder="https://calendly.com/your-link"
                        className={formData.errors.customCalendlyLink ? "border-red-500" : ""}
                      />
                      {formData.errors.customCalendlyLink && (
                        <p className="text-sm text-red-500">{formData.errors.customCalendlyLink}</p>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {selectedSalesRep && (
                <div className="mt-2 p-4 bg-gray-50 rounded-lg space-y-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <input
                      type="checkbox"
                      id="useCustomSalesRepUsername"
                      checked={formData.useCustomSalesRepUsername}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          useCustomSalesRepUsername: e.target.checked,
                          errors: { ...formData.errors, customSalesRepUsername: "" }
                        })
                      }
                      className="h-4 w-4"
                    />
                    <Label htmlFor="useCustomSalesRepUsername">Use custom Telegram username</Label>
                  </div>
                  
                  {formData.useCustomSalesRepUsername ? (
                    <div className="space-y-2">
                      <Label htmlFor="customSalesRepUsername">Custom Partnership Representative Username</Label>
                      <Input
                        id="customSalesRepUsername"
                        value={formData.customSalesRepUsername}
                        onChange={(e) =>
                          setFormData({ 
                            ...formData, 
                            customSalesRepUsername: e.target.value,
                            errors: { ...formData.errors, customSalesRepUsername: "" }
                          })
                        }
                        placeholder="@username"
                        className={formData.errors.customSalesRepUsername ? "border-red-500" : ""}
                      />
                      {formData.errors.customSalesRepUsername && (
                        <p className="text-sm text-red-500">{formData.errors.customSalesRepUsername}</p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm">
                      <span className="font-semibold">TG Username:</span> {selectedSalesRep.username}
                    </p>
                  )}
                  
                  <p className="text-sm">
                    <span className="font-semibold">PA Username:</span> {selectedSalesRep.paUsername}
                  </p>
                  
                  <div className="flex items-center space-x-4">
                    {/* Meeting Type select removed as per requirements */}
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="includeCalendlySelected"
                        checked={formData.includeCalendly}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            includeCalendly: e.target.checked
                          })
                        }
                        className="h-4 w-4"
                      />
                      <Label htmlFor="includeCalendlySelected">Include Calendly Link</Label>
                    </div>
                  </div>
                  {formData.includeCalendly && selectedSalesRep && (
                    <p className="text-sm">
                      <span className="font-semibold">Meeting Link:</span>{" "}
                      <a
                        href={selectedSalesRep?.calendarLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        Schedule Meeting
                      </a>
                    </p>
                  )}
                  {/* Meme team links removed - not available in current data structure */}
                  <p className="text-sm">
                    <span className="font-semibold">Pipeline:</span> {selectedSalesRep.tier}
                  </p>
                </div>
              )}
            </div>

            {/* Include Calendly Link Checkbox (re-added) */}
            <div className="flex items-center space-x-2 mt-2">
              <input
                type="checkbox"
                id="includeCalendlyGlobal"
                checked={formData.includeCalendly}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    includeCalendly: e.target.checked
                  })
                }
                className="h-4 w-4"
              />
              <Label htmlFor="includeCalendlyGlobal">Include Calendly Link</Label>
            </div>

            {/* Mandatory Members Note */}
            <div className="text-sm text-gray-500 p-4 bg-gray-50 rounded-lg">
              <p className="font-semibold mb-2">Group Creation Process:</p>
              <div className="space-y-4">
                <div>
                  <p className="font-semibold text-gray-700">1. Initial Members:</p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>@IbcAdmin_Bot (Bot Admin)</li>
                    {selectedSalesRep && selectedSalesRep.paUsername !== "None" && (
                      <li>{selectedSalesRep.paUsername} (PA - Will be made admin)</li>
                    )}
                    {selectedOutreachMember && formData.outreachMemberUsername && (
                      <li>{formData.outreachMemberUsername} ({selectedOutreachMember.emoji} Outreach Admin)</li>
                    )}
                    {formData.projectLeads.filter(s => s.trim().length > 0).length > 0 && (
                      <li>{formData.projectLeads.filter(s => s.trim().length > 0).join(", ")} (Project Lead)</li>
                    )}
                  </ul>
                </div>

                <div>
                  <p className="font-semibold text-gray-700">2. Automated Actions:</p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Welcome message will be sent by the IBC Admin account</li>
                    <li>Bot will execute /optin command</li>
                    {!formData.enterSalesRepManually && selectedSalesRep && selectedSalesRep.paUsername !== "None" && (
                      <li>PA will be made admin via /setadmin</li>
                    )}
                    {(formData.enterSalesRepManually && formData.customSalesRepUsername) || selectedSalesRep ? (
                      <li>Sales rep will be invited via their username: {formData.enterSalesRepManually ? formData.customSalesRepUsername : (formData.useCustomSalesRepUsername && formData.customSalesRepUsername ? formData.customSalesRepUsername : selectedSalesRep?.username)}</li>
                    ) : null}
                    {(formData.enterSalesRepManually && formData.customSalesRepUsername) || selectedSalesRep ? (
                      <li>Bot will execute /invite_new_group for the sales rep</li>
                    ) : null}
                  </ul>
                </div>

                <div>
                  <p className="font-semibold text-gray-700">3. Group Settings:</p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Group Name: {formData.projectName ? getGroupName(formData.projectName, selectedSalesRep) : "Will be set based on project name"}</li>
                    <li>Type: Supergroup (Megagroup)</li>
                    <li>About: IBC Group Discussion</li>
                  </ul>
                </div>

                {/* Welcome Message Preview Section */}
                <div>
                  <p className="font-semibold text-gray-700">4. Welcome Message:</p>
                  {((selectedSalesRep || (formData.enterSalesRepManually && formData.customSalesRepUsername)) &&
                    formData.projectLeads.filter(s => s.trim().length > 0).length > 0 &&
                    (formData.outreachMemberUsername || (formData.useCustomOutreachUsername && formData.customOutreachUsername))) ? (
                    <div className="bg-white p-3 rounded border mt-2 text-gray-800 font-mono text-sm">
                      <div>👥 Project Lead: {formData.projectLeads.filter(s => s.trim().length > 0).join(", ")}</div>
                      <div>👨‍💼 Partnership Representative: {formData.enterSalesRepManually ? formData.customSalesRepUsername : (formData.useCustomSalesRepUsername && formData.customSalesRepUsername ? formData.customSalesRepUsername : selectedSalesRep?.username)}</div>
                      <div>👨‍💻 BDR: {formData.useCustomOutreachUsername ? formData.customOutreachUsername : formData.outreachMemberUsername}</div>
                      {formData.includeCalendly && formData.enterSalesRepManually && formData.customCalendlyLink && (
                        <div>📅 Schedule a meeting: {formData.customCalendlyLink}</div>
                      )}
                      {formData.includeCalendly && !formData.enterSalesRepManually && selectedSalesRep && (
                        <div>📅 Schedule a meeting: {selectedSalesRep?.calendarLink}</div>
                      )}
                    </div>
                  ) : (
                    <div className="bg-white p-3 rounded border mt-2 text-gray-800 font-mono text-sm opacity-50">
                      <div>👥 Project Lead: (Enter project lead)</div>
                      <div>👨‍💼 Partnership Representative: (Select representative)</div>
                      <div>👨‍💻 BDR: (Select BDR)</div>
                      <div>📅 Schedule a meeting: (Will show if enabled)</div>
                    </div>
                  )}
                </div>

                {/* Sales Rep Details Section */}
                <div>
                  <p className="font-semibold text-gray-700">
                    {(selectedSalesRep || (formData.enterSalesRepManually && formData.customSalesRepUsername)) &&
                     formData.projectLeads.filter(s => s.trim().length > 0).length > 0 &&
                     (formData.outreachMemberUsername || (formData.useCustomOutreachUsername && formData.customOutreachUsername))
                      ? "5" : "4"}. Sales Rep Details:
                  </p>
                  
                  {(selectedSalesRep || (formData.enterSalesRepManually && formData.customSalesRepUsername)) ? (
                    <ul className="list-none space-y-1 ml-4">
                      <li>
                        <span className="font-medium">TG Username:</span>{" "}
                        <span className="text-blue-600">
                          {formData.enterSalesRepManually 
                            ? formData.customSalesRepUsername 
                            : (formData.useCustomSalesRepUsername && formData.customSalesRepUsername 
                                ? formData.customSalesRepUsername 
                                : selectedSalesRep?.username)}
                        </span>
                        {(formData.enterSalesRepManually || (formData.useCustomSalesRepUsername && formData.customSalesRepUsername)) && (
                          <span className="ml-2 text-sm text-orange-600">(Custom)</span>
                        )}
                      </li>
                      
                      {/* Conditionally render meeting link for manual sales rep */}
                      {formData.enterSalesRepManually && formData.includeCalendly && formData.customCalendlyLink && (
                        <li>
                          <span className="font-medium">Meeting Link:</span>{" "}
                          <a 
                            href={formData.customCalendlyLink} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline"
                          >
                            Schedule Meeting
                          </a>
                        </li>
                      )}
                      
                      {/* Conditionally render selected sales rep details */}
                      {!formData.enterSalesRepManually && selectedSalesRep && (
                        <>
                          <li>
                            <span className="font-medium">PA Username:</span>{" "}
                            <span className="text-blue-600">{selectedSalesRep.paUsername}</span>
                          </li>
                          <li>
                            <span className="font-medium">Pipeline:</span>{" "}
                            <span className="text-blue-600">{selectedSalesRep.tier}</span>
                          </li>
                          {formData.includeCalendly && (
                            <li>
                              <span className="font-medium">Meeting Link:</span>{" "}
                              <a 
                                href={selectedSalesRep?.calendarLink}
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:underline"
                              >
                                Schedule Meeting
                              </a>
                            </li>
                          )}
                        </>
                      )}
                    </ul>
                  ) : (
                    <ul className="list-none space-y-1 ml-4 opacity-50">
                      <li><span className="font-medium">TG Username:</span> (Select or enter sales rep)</li>
                      <li><span className="font-medium">Details:</span> Additional details will appear here</li>
                    </ul>
                  )}
                </div>
              </div>
            </div>

            {/* Submit Button with Loading State */}
            <Button 
              type="submit" 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              disabled={Object.values(formData.errors).some(error => error !== "")}
            >
              Create Telegram Group
            </Button>

            {/* Help Text */}
            <p className="text-xs text-gray-500 text-center">
              This will create a new Telegram group with all required members and settings.
              The process takes a few seconds to complete.
            </p>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 
