/**
 * Custom error classes for better error handling and debugging
 */

export class TelegramError extends Error {
  public readonly code?: string;
  public readonly statusCode: number;

  constructor(message: string, code?: string, statusCode: number = 500) {
    super(message);
    this.name = 'TelegramError';
    this.code = code;
    this.statusCode = statusCode;
  }

  static fromTelegramApiError(error: any): TelegramError {
    const message = error.message || 'Unknown Telegram API error';
    const lowerMessage = message.toLowerCase();

    // Rate limiting errors
    if (lowerMessage.includes('flood')) {
      return new TelegramError(
        'Too many requests. Please wait before trying again.',
        'FLOOD_WAIT',
        429
      );
    }

    // Permission errors
    if (lowerMessage.includes('not enough rights') || lowerMessage.includes('insufficient rights')) {
      return new TelegramError(
        'Bo<PERSON> lacks admin permissions to add users to this group.',
        'INSUFFICIENT_RIGHTS',
        403
      );
    }

    // Session errors
    if (lowerMessage.includes('session')) {
      return new TelegramError(
        'Telegram session expired. Please regenerate session string.',
        'SESSION_EXPIRED',
        401
      );
    }

    // User-related errors
    if (lowerMessage.includes('user not found') || lowerMessage.includes('username not found')) {
      return new TelegramError(
        'Username not found. User may have changed their username or deleted their account.',
        'USER_NOT_FOUND',
        404
      );
    }

    if (lowerMessage.includes('user_privacy_restricted') || lowerMessage.includes('privacy')) {
      return new TelegramError(
        'User has privacy settings that prevent being added to groups.',
        'USER_PRIVACY_RESTRICTED',
        403
      );
    }

    if (lowerMessage.includes('user_not_mutual_contact') || lowerMessage.includes('not mutual contact')) {
      return new TelegramError(
        'User can only be added by mutual contacts due to their privacy settings.',
        'USER_NOT_MUTUAL_CONTACT',
        403
      );
    }

    if (lowerMessage.includes('user_already_participant') || lowerMessage.includes('already participant')) {
      return new TelegramError(
        'User is already a member of this group.',
        'USER_ALREADY_PARTICIPANT',
        409
      );
    }

    if (lowerMessage.includes('user_blocked') || lowerMessage.includes('blocked')) {
      return new TelegramError(
        'User has blocked the bot or restricted group invitations.',
        'USER_BLOCKED',
        403
      );
    }

    if (lowerMessage.includes('user_bot') || lowerMessage.includes('bot')) {
      return new TelegramError(
        'Cannot add bots to this group type.',
        'USER_BOT',
        400
      );
    }

    if (lowerMessage.includes('user_channels_too_much') || lowerMessage.includes('too many channels')) {
      return new TelegramError(
        'User has joined too many channels/groups and cannot join more.',
        'USER_CHANNELS_TOO_MUCH',
        400
      );
    }

    // Chat-related errors
    if (lowerMessage.includes('chat not found')) {
      return new TelegramError(
        'Chat not found or bot was removed from chat.',
        'CHAT_NOT_FOUND',
        404
      );
    }

    if (lowerMessage.includes('chat_admin_required') || lowerMessage.includes('admin required')) {
      return new TelegramError(
        'Admin rights required to perform this action.',
        'CHAT_ADMIN_REQUIRED',
        403
      );
    }

    // Network/connection errors
    if (lowerMessage.includes('timeout') || lowerMessage.includes('connection')) {
      return new TelegramError(
        'Connection timeout. Please try again.',
        'CONNECTION_TIMEOUT',
        408
      );
    }

    // Generic invite errors
    if (lowerMessage.includes('invite') && lowerMessage.includes('invalid')) {
      return new TelegramError(
        'Invalid invitation. User may have restricted group invitations.',
        'INVALID_INVITE',
        400
      );
    }

    return new TelegramError(
      `Telegram API error: ${message}`,
      'UNKNOWN_ERROR',
      500
    );
  }
}

export class GoogleSheetsError extends Error {
  public readonly sheetName?: string;
  public readonly statusCode: number;

  constructor(message: string, sheetName?: string, statusCode: number = 500) {
    super(message);
    this.name = 'GoogleSheetsError';
    this.sheetName = sheetName;
    this.statusCode = statusCode;
  }

  static fromGoogleApiError(error: any, sheetName?: string): GoogleSheetsError {
    const message = error.message || 'Unknown Google Sheets API error';
    
    if (error.code === 403) {
      return new GoogleSheetsError(
        'Access denied to Google Sheets. Please check service account permissions.',
        sheetName,
        403
      );
    }
    
    if (error.code === 404) {
      return new GoogleSheetsError(
        `Spreadsheet or sheet "${sheetName}" not found. Please check the spreadsheet ID and sheet name.`,
        sheetName,
        404
      );
    }
    
    if (error.code === 400) {
      return new GoogleSheetsError(
        `Invalid request to Google Sheets API: ${message}`,
        sheetName,
        400
      );
    }
    
    return new GoogleSheetsError(
      `Google Sheets API error: ${message}`,
      sheetName,
      500
    );
  }
}

export class ValidationError extends Error {
  public readonly field?: string;
  public readonly statusCode: number = 400;

  constructor(message: string, field?: string) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class AuthenticationError extends Error {
  public readonly statusCode: number = 401;

  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  public readonly statusCode: number = 403;

  constructor(message: string = 'Insufficient permissions') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class RateLimitError extends Error {
  public readonly statusCode: number = 429;
  public readonly retryAfter?: number;

  constructor(message: string = 'Rate limit exceeded', retryAfter?: number) {
    super(message);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

/**
 * Error handler utility functions
 */
export function isOperationalError(error: Error): boolean {
  return (
    error instanceof TelegramError ||
    error instanceof GoogleSheetsError ||
    error instanceof ValidationError ||
    error instanceof AuthenticationError ||
    error instanceof AuthorizationError ||
    error instanceof RateLimitError
  );
}

export function getErrorStatusCode(error: Error): number {
  if ('statusCode' in error && typeof error.statusCode === 'number') {
    return error.statusCode;
  }
  return 500;
}

export function getErrorMessage(error: Error): string {
  if (isOperationalError(error)) {
    return error.message;
  }
  
  // Don't expose internal error details in production
  if (process.env.NODE_ENV === 'production') {
    return 'An unexpected error occurred. Please try again later.';
  }
  
  return error.message || 'Unknown error occurred';
}

/**
 * Log error with appropriate level and context
 */
export function logError(error: Error, context?: Record<string, any>): void {
  const errorInfo = {
    name: error.name,
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString(),
  };

  if (isOperationalError(error)) {
    console.warn('⚠️ Operational error:', errorInfo);
  } else {
    console.error('❌ Unexpected error:', errorInfo);
  }
}

/**
 * Create error response for API routes
 */
export function createErrorResponse(error: Error, context?: Record<string, any>) {
  logError(error, context);
  
  return {
    success: false,
    error: getErrorMessage(error),
    code: 'code' in error ? error.code : undefined,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Async error wrapper for API routes
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(String(error));
    }
  };
}

/**
 * Retry wrapper with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Don't retry on certain errors
      if (
        lastError instanceof ValidationError ||
        lastError instanceof AuthenticationError ||
        lastError instanceof AuthorizationError
      ) {
        throw lastError;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`⏳ Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}
