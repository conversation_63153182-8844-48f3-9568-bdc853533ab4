import { NextRequest } from 'next/server';

/**
 * Audit Logger for Security Monitoring and Compliance
 * Tracks user actions, group creation, and security events
 */

export interface AuditLogEntry {
  id: string;
  timestamp: string;
  userId?: string;
  userEmail?: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  success: boolean;
  errorMessage?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'auth' | 'group_creation' | 'data_access' | 'security' | 'system';
}

export interface SecurityEvent {
  type: 'rate_limit_exceeded' | 'invalid_csrf' | 'unauthorized_access' | 'suspicious_activity';
  details: Record<string, any>;
  severity: 'medium' | 'high' | 'critical';
}

class AuditLogger {
  private logs: AuditLogEntry[] = [];
  private maxLogs = 10000; // Keep last 10k logs in memory
  
  /**
   * Generate unique ID for audit log entry
   */
  private generateId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extract request metadata for audit logging
   */
  private extractRequestMetadata(req?: NextRequest | Request) {
    if (!req) return {};
    
    const headers = req.headers;
    return {
      ipAddress: headers.get('x-forwarded-for') || 
                headers.get('x-real-ip') || 
                'unknown',
      userAgent: headers.get('user-agent') || 'unknown',
      referer: headers.get('referer'),
    };
  }

  /**
   * Log a general audit event
   */
  log(entry: Omit<AuditLogEntry, 'id' | 'timestamp'>): void {
    const auditEntry: AuditLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      ...entry,
    };

    this.logs.push(auditEntry);
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console logging with structured format
    const logLevel = this.getLogLevel(entry.severity);
    console[logLevel]('🔍 AUDIT LOG:', {
      id: auditEntry.id,
      timestamp: auditEntry.timestamp,
      userId: auditEntry.userId,
      action: auditEntry.action,
      resource: auditEntry.resource,
      success: auditEntry.success,
      severity: auditEntry.severity,
      category: auditEntry.category,
      details: auditEntry.details,
    });

    // In production, you would send this to your logging service
    // Examples: DataDog, Splunk, ELK Stack, CloudWatch, etc.
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalLogger(auditEntry);
    }
  }

  /**
   * Log group creation events
   */
  logGroupCreation(params: {
    userId?: string;
    userEmail?: string;
    projectName: string;
    groupId?: string;
    salesRep: string;
    outreachMembers: string[];
    success: boolean;
    errorMessage?: string;
    req?: NextRequest | Request;
  }): void {
    this.log({
      userId: params.userId,
      userEmail: params.userEmail,
      action: 'create_telegram_group',
      resource: 'telegram_group',
      resourceId: params.groupId,
      details: {
        projectName: params.projectName,
        salesRep: params.salesRep,
        outreachMembersCount: params.outreachMembers.length,
        outreachMembers: params.outreachMembers,
      },
      success: params.success,
      errorMessage: params.errorMessage,
      severity: params.success ? 'low' : 'medium',
      category: 'group_creation',
      ...this.extractRequestMetadata(params.req),
    });
  }

  /**
   * Log data access events
   */
  logDataAccess(params: {
    userId?: string;
    userEmail?: string;
    action: string;
    resource: string;
    success: boolean;
    errorMessage?: string;
    req?: NextRequest | Request;
  }): void {
    this.log({
      userId: params.userId,
      userEmail: params.userEmail,
      action: params.action,
      resource: params.resource,
      details: {},
      success: params.success,
      errorMessage: params.errorMessage,
      severity: params.success ? 'low' : 'medium',
      category: 'data_access',
      ...this.extractRequestMetadata(params.req),
    });
  }

  /**
   * Log security events
   */
  logSecurityEvent(params: {
    userId?: string;
    userEmail?: string;
    event: SecurityEvent;
    req?: NextRequest | Request;
  }): void {
    this.log({
      userId: params.userId,
      userEmail: params.userEmail,
      action: 'security_event',
      resource: 'security',
      details: {
        eventType: params.event.type,
        ...params.event.details,
      },
      success: false, // Security events are typically failures/alerts
      severity: params.event.severity,
      category: 'security',
      ...this.extractRequestMetadata(params.req),
    });
  }

  /**
   * Log authentication events
   */
  logAuthEvent(params: {
    userId?: string;
    userEmail?: string;
    action: 'login' | 'logout' | 'login_failed' | 'session_expired';
    success: boolean;
    errorMessage?: string;
    req?: NextRequest | Request;
  }): void {
    this.log({
      userId: params.userId,
      userEmail: params.userEmail,
      action: params.action,
      resource: 'authentication',
      details: {},
      success: params.success,
      errorMessage: params.errorMessage,
      severity: params.success ? 'low' : 'medium',
      category: 'auth',
      ...this.extractRequestMetadata(params.req),
    });
  }

  /**
   * Get console log level based on severity
   */
  private getLogLevel(severity: string): 'log' | 'warn' | 'error' {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warn';
      default:
        return 'log';
    }
  }

  /**
   * Send audit log to external logging service
   * In production, implement integration with your logging provider
   */
  private async sendToExternalLogger(entry: AuditLogEntry): Promise<void> {
    try {
      // Example implementations:
      
      // For DataDog:
      // await fetch('https://http-intake.logs.datadoghq.com/v1/input/YOUR_API_KEY', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(entry)
      // });

      // For CloudWatch:
      // const cloudWatchLogs = new AWS.CloudWatchLogs();
      // await cloudWatchLogs.putLogEvents({
      //   logGroupName: '/aws/lambda/audit-logs',
      //   logStreamName: 'audit-stream',
      //   logEvents: [{ timestamp: Date.now(), message: JSON.stringify(entry) }]
      // }).promise();

      // For now, just log that we would send to external service
      console.log('📤 Would send to external logging service:', entry.id);
    } catch (error) {
      console.error('❌ Failed to send audit log to external service:', error);
    }
  }

  /**
   * Get audit logs (for admin dashboard or debugging)
   */
  getLogs(filters?: {
    userId?: string;
    action?: string;
    category?: string;
    severity?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): AuditLogEntry[] {
    let filteredLogs = [...this.logs];

    if (filters) {
      if (filters.userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
      }
      if (filters.action) {
        filteredLogs = filteredLogs.filter(log => log.action === filters.action);
      }
      if (filters.category) {
        filteredLogs = filteredLogs.filter(log => log.category === filters.category);
      }
      if (filters.severity) {
        filteredLogs = filteredLogs.filter(log => log.severity === filters.severity);
      }
      if (filters.startDate) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.startDate!);
      }
      if (filters.endDate) {
        filteredLogs = filteredLogs.filter(log => log.timestamp <= filters.endDate!);
      }
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Apply limit
    if (filters?.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit);
    }

    return filteredLogs;
  }

  /**
   * Get security summary for monitoring dashboard
   */
  getSecuritySummary(timeRange: '1h' | '24h' | '7d' = '24h'): {
    totalEvents: number;
    securityEvents: number;
    failedActions: number;
    criticalEvents: number;
    topActions: Array<{ action: string; count: number }>;
    topUsers: Array<{ userId: string; count: number }>;
  } {
    const now = new Date();
    const timeRangeMs = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
    }[timeRange];

    const cutoffTime = new Date(now.getTime() - timeRangeMs).toISOString();
    const recentLogs = this.logs.filter(log => log.timestamp >= cutoffTime);

    const securityEvents = recentLogs.filter(log => log.category === 'security').length;
    const failedActions = recentLogs.filter(log => !log.success).length;
    const criticalEvents = recentLogs.filter(log => log.severity === 'critical').length;

    // Count actions
    const actionCounts: Record<string, number> = {};
    const userCounts: Record<string, number> = {};

    recentLogs.forEach(log => {
      actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
      if (log.userId) {
        userCounts[log.userId] = (userCounts[log.userId] || 0) + 1;
      }
    });

    const topActions = Object.entries(actionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([action, count]) => ({ action, count }));

    const topUsers = Object.entries(userCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([userId, count]) => ({ userId, count }));

    return {
      totalEvents: recentLogs.length,
      securityEvents,
      failedActions,
      criticalEvents,
      topActions,
      topUsers,
    };
  }
}

// Singleton instance
export const auditLogger = new AuditLogger();

/**
 * Middleware helper to automatically log API requests
 */
export function withAuditLogging<T extends (...args: any[]) => any>(
  handler: T,
  action: string,
  resource: string
): T {
  return (async (...args: any[]) => {
    const req = args[0] as NextRequest | Request;
    const startTime = Date.now();
    
    try {
      const result = await handler(...args);
      
      auditLogger.log({
        action,
        resource,
        details: {
          duration: Date.now() - startTime,
          method: req.method,
          url: req.url,
        },
        success: true,
        severity: 'low',
        category: 'system',
        ...auditLogger['extractRequestMetadata'](req),
      });
      
      return result;
    } catch (error: any) {
      auditLogger.log({
        action,
        resource,
        details: {
          duration: Date.now() - startTime,
          method: req.method,
          url: req.url,
        },
        success: false,
        errorMessage: error.message,
        severity: 'medium',
        category: 'system',
        ...auditLogger['extractRequestMetadata'](req),
      });
      
      throw error;
    }
  }) as T;
}
