openapi: 3.0.3
info:
  title: Telegram Group Creator API
  description: |
    API for creating and managing Telegram groups with automated setup.
    
    ## Features
    - Automated Telegram group creation
    - Team member management via Google Sheets
    - Rate limiting and security features
    - Comprehensive audit logging
    
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://your-domain.com/api
    description: Production server
  - url: http://localhost:3000/api
    description: Development server

security:
  - ClerkAuth: []

paths:
  /health:
    get:
      summary: Health Check
      description: Check the health status of the Telegram bot connection
      operationId: getHealth
      tags:
        - Health
      responses:
        '200':
          description: Health status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  user_info:
                    type: string
                    description: JSON string containing bot information
                    example: '{"id":"bot_id","username":"bot_username","firstName":"Bot Name"}'
        '500':
          $ref: '#/components/responses/ServerError'

  /team-data:
    get:
      summary: Get Team Data
      description: Retrieve sales representatives and outreach members from Google Sheets
      operationId: getTeamData
      tags:
        - Team
      responses:
        '200':
          description: Team data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamDataResponse'
        '500':
          $ref: '#/components/responses/ServerError'

  /create-group:
    post:
      summary: Create Telegram Group
      description: Create a new Telegram group with automated setup and member invitations
      operationId: createGroup
      tags:
        - Groups
      security:
        - ClerkAuth: []
        - CSRFToken: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateGroupRequest'
      responses:
        '200':
          description: Group created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateGroupResponse'
        '400':
          $ref: '#/components/responses/ValidationError'
        '429':
          $ref: '#/components/responses/RateLimitError'
        '500':
          $ref: '#/components/responses/ServerError'

  /csrf-token:
    get:
      summary: Get CSRF Token
      description: Retrieve a CSRF token for secure form submissions
      operationId: getCSRFToken
      tags:
        - Security
      responses:
        '200':
          description: CSRF token generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                    example: "a1b2c3d4e5f6..."

components:
  securitySchemes:
    ClerkAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Clerk session token
    CSRFToken:
      type: apiKey
      in: header
      name: X-CSRF-Token
      description: CSRF protection token

  schemas:
    TeamDataResponse:
      type: object
      properties:
        salesReps:
          type: array
          items:
            $ref: '#/components/schemas/SalesRep'
        outreachMembers:
          type: array
          items:
            $ref: '#/components/schemas/OutreachMember'
        warning:
          type: string
          description: Warning message if Google Sheets is not configured
          example: "Google Sheets integration not configured"

    SalesRep:
      type: object
      required:
        - name
        - username
      properties:
        name:
          type: string
          example: "John Doe"
        username:
          type: string
          pattern: '^@[a-zA-Z0-9_]{1,32}$'
          example: "@johndoe"
        personalAssistant:
          type: string
          pattern: '^@[a-zA-Z0-9_]{1,32}$'
          example: "@johndoe_pa"

    OutreachMember:
      type: object
      required:
        - name
        - username
      properties:
        name:
          type: string
          example: "Alice Johnson"
        username:
          type: string
          pattern: '^@[a-zA-Z0-9_]{1,32}$'
          example: "@alicejohnson"

    CreateGroupRequest:
      type: object
      required:
        - projectName
        - salesRepUsername
        - outreachMemberUsernames
      properties:
        projectName:
          type: string
          minLength: 1
          maxLength: 100
          description: Name of the project
          example: "My Awesome Project"
        salesRepUsername:
          type: string
          pattern: '^@[a-zA-Z0-9_]{1,32}$'
          description: Sales representative Telegram username
          example: "@johndoe"
        outreachMemberUsernames:
          type: array
          minItems: 1
          maxItems: 20
          items:
            type: string
            pattern: '^@[a-zA-Z0-9_]{1,32}$'
          description: Array of outreach member Telegram usernames
          example: ["@alice", "@bob"]

    CreateGroupResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        group_id:
          type: string
          example: "1234567890"
        group_name:
          type: string
          example: "My Awesome Project - IBC Group"
        message:
          type: string
          example: "Group created successfully"
        added_members:
          type: object
          properties:
            admins:
              type: array
              items:
                type: string
              example: ["@johndoe"]
            regular:
              type: array
              items:
                type: string
              example: ["@alice", "@bob"]
        failed_invites:
          type: array
          items:
            $ref: '#/components/schemas/FailedInvite'

    FailedInvite:
      type: object
      properties:
        username:
          type: string
          example: "@bob"
        reason:
          type: string
          example: "User not found"

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
        details:
          type: string
          description: Additional error details
        code:
          type: string
          description: Error code
        timestamp:
          type: string
          format: date-time
          description: Error timestamp

    ValidationError:
      allOf:
        - $ref: '#/components/schemas/Error'
        - type: object
          properties:
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                  message:
                    type: string

  responses:
    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationError'
          example:
            error: "Validation failed"
            details:
              - field: "projectName"
                message: "Project name must be at least 1 character long"

    RateLimitError:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "Rate limit exceeded"
            message: "Too many group creation attempts. Please try again later."

    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "Internal server error"
            details: "An unexpected error occurred"

tags:
  - name: Health
    description: Health check operations
  - name: Team
    description: Team data operations
  - name: Groups
    description: Telegram group operations
  - name: Security
    description: Security and authentication operations
