{"name": "custom-group-creator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "next build", "telegram:session": "ts-node --project scripts/tsconfig.json scripts/getTelegramSession.ts", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest --watch"}, "dependencies": {"@clerk/nextjs": "4.29.7", "@radix-ui/react-label": "2.0.2", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-slot": "1.0.2", "@supabase/supabase-js": "^2.48.1", "axios": "1.6.7", "class-variance-authority": "0.7.0", "clsx": "2.1.0", "dotenv": "^16.4.7", "googleapis": "^148.0.0", "lottie-react": "^2.4.1", "lucide-react": "0.331.0", "next": "14.1.0", "react": "18.2.0", "react-dom": "18.2.0", "sonner": "1.4.0", "tailwind-merge": "2.2.1", "tailwindcss-animate": "1.0.7", "telegram": "^2.26.22", "ts-node": "^10.9.2", "zod": "^3.25.67"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.12.0", "@types/react": "18.2.56", "@types/react-dom": "18.2.19", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "10.4.17", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "jsdom": "^26.1.0", "msw": "^2.10.2", "postcss": "8.4.35", "tailwindcss": "3.4.1", "typescript": "5.3.3", "vitest": "^2.1.8"}}