{"permissions": {"allow": ["Bash(bun run:*)", "Bash(npx tsc:*)", "Bash(npx tsc:*)", "Bash(npx tsc:*)", "mcp__ide__getDiagnostics", "Bash(npx eslint:*)", "Bash(grep:*)", "Bash(bun test:*)", "Bash(bunx eslint:*)", "Bash(bunx next lint:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(bun install:*)", "Bash(rm:*)", "Bash(git commit:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(rg:*)"], "deny": []}}